<table class="w-full border-collapse">
  <caption></caption>
  <tr class="border-b border-border-divider-alternative odd:bg-background even:bg-background-2">
    <th
      class="bg-background px-4 py-2 text-left font-bold"
      [innerText]="'register_file.labels.filed_process.filed_process_data.taxes.taxId' | translate"
    ></th>
    <th
      class="bg-background px-4 py-2 text-left font-bold"
      [innerText]="'register_file.labels.filed_process.filed_process_data.taxes.federalState' | translate"
    ></th>
    <th
      class="bg-background px-4 py-2 text-left font-bold"
      [innerText]="'register_file.labels.filed_process.filed_process_data.taxes.validFrom' | translate"
    ></th>
    <th
      class="bg-background px-4 py-2 text-left font-bold"
      [innerText]="'register_file.labels.filed_process.filed_process_data.taxes.validTo' | translate"
    ></th>
    <th
      class="bg-background px-4 py-2 text-left font-bold"
      [innerText]="'register_file.labels.filed_process.filed_process_data.taxes.paymentInfo.amount' | translate"
    ></th>
    <th
      class="bg-background px-4 py-2 text-left font-bold"
      [innerText]="'register_file.labels.filed_process.filed_process_data.taxes.paymentInfo.type' | translate"
    ></th>
  </tr>
  @for (dataEntry of data(); track dataEntry) {
    <tr
      fish-register-file-table-row
      [cells]="[
        dataEntry.taxId,
        dataEntry.federalState,
        dataEntry.validFrom,
        dataEntry?.validTo,
        dataEntry.paymentInfo?.amount,
        dataEntry.paymentInfo.type,
      ]"
    ></tr>
  }
</table>
