import { ChangeDetectionStrategy, Component, input } from '@angular/core';

import { TranslateModule } from '@ngx-translate/core';

import { FiledProcess, PersonFP } from '@digifischdok/ngx-register-sdk';

import { RegisterFileBanTableComponent } from '@/app/features/register-file/components/molecules/register-file-ban-table/register-file-ban-table.component';
import { RegisterFileConsentInfoTableComponent } from '@/app/features/register-file/components/molecules/register-file-consent-info-table/register-file-consent-info-table.component';
import { RegisterFileDeletionFlagTableComponent } from '@/app/features/register-file/components/molecules/register-file-deletion-flag-table/register-file-deletion-flag-table.component';
import { RegisterFileFeesTableComponent } from '@/app/features/register-file/components/molecules/register-file-fees-table/register-file-fees-table.component';
import { RegisterFileFishingLicenseTableComponent } from '@/app/features/register-file/components/molecules/register-file-fishing-license-table/register-file-fishing-license-table.component';
import { RegisterFileIdentificationDocumentsTableComponent } from '@/app/features/register-file/components/molecules/register-file-identification-documents-table/register-file-identification-documents-table.component';
import { RegisterFileOfficeAddressTableComponent } from '@/app/features/register-file/components/molecules/register-file-office-address-table/register-file-office-address-table.component';
import { RegisterFilePersonTableComponent } from '@/app/features/register-file/components/molecules/register-file-person-table/register-file-person-table.component';
import { RegisterFileProcessHeadTableComponent } from '@/app/features/register-file/components/molecules/register-file-process-head-table/register-file-process-head-table.component';
import { RegisterFileQualificationsProofTableComponent } from '@/app/features/register-file/components/molecules/register-file-qualifications-proof-table/register-file-qualifications-proof-table.component';
import { RegisterFileTaxesTableComponent } from '@/app/features/register-file/components/molecules/register-file-taxes-table/register-file-taxes-table.component';
import { RegisterFileValidityPeriodsTableComponent } from '@/app/features/register-file/components/molecules/register-file-validity-periods-table/register-file-validity-periods-table.component';

@Component({
  selector: 'fish-register-file-processes',
  imports: [
    TranslateModule,
    RegisterFileProcessHeadTableComponent,
    RegisterFilePersonTableComponent,
    RegisterFileOfficeAddressTableComponent,
    RegisterFileQualificationsProofTableComponent,
    RegisterFileFishingLicenseTableComponent,
    RegisterFileBanTableComponent,
    RegisterFileConsentInfoTableComponent,
    RegisterFileFeesTableComponent,
    RegisterFileTaxesTableComponent,
    RegisterFileValidityPeriodsTableComponent,
    RegisterFileIdentificationDocumentsTableComponent,
    RegisterFileDeletionFlagTableComponent,
  ],
  templateUrl: './register-file-processes.component.html',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class RegisterFileProcessesComponent {
  public readonly processes = input.required<FiledProcess[]>();

  protected personalDataWithServiceAccountId(
    person: PersonFP,
    serviceAccountId: string | undefined
  ): PersonFP & {
    serviceAccountId: string | undefined;
  } {
    return {
      ...person,
      serviceAccountId,
    };
  }
}
