<table class="w-full border-collapse">
  <caption></caption>
  <tr class="border-b border-border-divider-alternative odd:bg-background even:bg-background-2">
    <th class="bg-background px-4 py-2 text-left font-bold" [innerText]="'register_file.titles.data_table.field' | translate"></th>
    <th class="bg-background px-4 py-2 text-left font-bold" [innerText]="'register_file.titles.data_table.value' | translate"></th>
  </tr>
  <tr fish-register-file-table-row [cells]="['register_file.labels.filed_process.filed_process_data.person.title' | translate, data().title]"></tr>
  <tr
    fish-register-file-table-row
    [cells]="['register_file.labels.filed_process.filed_process_data.person.firstname' | translate, data().firstname]"
  ></tr>
  <tr
    fish-register-file-table-row
    [cells]="['register_file.labels.filed_process.filed_process_data.person.lastname' | translate, data().lastname]"
  ></tr>
  <tr
    fish-register-file-table-row
    [cells]="['register_file.labels.filed_process.filed_process_data.person.birthdate' | translate, data().birthdate]"
  ></tr>
  <tr
    fish-register-file-table-row
    [cells]="['register_file.labels.filed_process.filed_process_data.person.birthname' | translate, data().birthname]"
  ></tr>
  <tr
    fish-register-file-table-row
    [cells]="['register_file.labels.filed_process.filed_process_data.person.birthplace' | translate, data().birthplace]"
  ></tr>
  <tr
    fish-register-file-table-row
    [cells]="['register_file.labels.filed_process.filed_process_data.person.nationality' | translate, data().nationality]"
  ></tr>
  <tr fish-register-file-table-row [cells]="['register_file.labels.filed_process.filed_process_data.person.email' | translate, data().email]"></tr>
  <tr
    fish-register-file-table-row
    [cells]="['register_file.labels.filed_process.filed_process_data.person.address.street' | translate, data().address?.street]"
  ></tr>
  <tr
    fish-register-file-table-row
    [cells]="['register_file.labels.filed_process.filed_process_data.person.address.streetNumber' | translate, data().address?.streetNumber]"
  ></tr>
  <tr
    fish-register-file-table-row
    [cells]="['register_file.labels.filed_process.filed_process_data.person.address.postcode' | translate, data().address?.postcode]"
  ></tr>
  <tr
    fish-register-file-table-row
    [cells]="['register_file.labels.filed_process.filed_process_data.person.address.city' | translate, data().address?.city]"
  ></tr>
  <tr
    fish-register-file-table-row
    [cells]="['register_file.labels.filed_process.filed_process_data.person.address.detail' | translate, data().address?.detail]"
  ></tr>
  <tr
    fish-register-file-table-row
    [cells]="['register_file.labels.filed_process.filed_process_data.person.serviceAccountId' | translate, data().serviceAccountId]"
  ></tr>
</table>
