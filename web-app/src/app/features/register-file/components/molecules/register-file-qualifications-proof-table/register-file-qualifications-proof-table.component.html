<table class="w-full border-collapse">
  <caption></caption>
  <tr class="border-b border-border-divider-alternative odd:bg-background even:bg-background-2">
    <th class="bg-background px-4 py-2 text-left font-bold" [innerText]="'register_file.titles.data_table.field' | translate"></th>
    <th class="bg-background px-4 py-2 text-left font-bold" [innerText]="'register_file.titles.data_table.value' | translate"></th>
  </tr>
  <tr
    fish-register-file-table-row
    [cells]="['register_file.labels.filed_process.filed_process_data.qualificationsProof.type' | translate, data().type]"
  ></tr>
  <tr
    fish-register-file-table-row
    [cells]="[
      'register_file.labels.filed_process.filed_process_data.qualificationsProof.fishingCertificateId' | translate,
      data().fishingCertificateId,
    ]"
  ></tr>
  <tr
    fish-register-file-table-row
    [cells]="['register_file.labels.filed_process.filed_process_data.qualificationsProof.otherFormOfProofId' | translate, data().otherFormOfProofId]"
  ></tr>
  <tr
    fish-register-file-table-row
    [cells]="['register_file.labels.filed_process.filed_process_data.qualificationsProof.federalState' | translate, data().federalState]"
  ></tr>
  <tr
    fish-register-file-table-row
    [cells]="['register_file.labels.filed_process.filed_process_data.qualificationsProof.passedOn' | translate, data().passedOn]"
  ></tr>
  <tr
    fish-register-file-table-row
    [cells]="['register_file.labels.filed_process.filed_process_data.qualificationsProof.issuedBy' | translate, data().issuedBy]"
  ></tr>
</table>
