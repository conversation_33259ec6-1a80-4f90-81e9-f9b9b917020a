<table class="w-full border-collapse">
  <caption></caption>
  <tr class="border-b border-border-divider-alternative odd:bg-background even:bg-background-2">
    <th class="bg-background px-4 py-2 text-left font-bold" [innerText]="'register_file.titles.data_table.field' | translate"></th>
    <th class="bg-background px-4 py-2 text-left font-bold" [innerText]="'register_file.titles.data_table.value' | translate"></th>
  </tr>
  <tr
    fish-register-file-table-row
    [cells]="['register_file.labels.filed_process.filed_process_data.officeAddress.office' | translate, data().office]"
  ></tr>
  <tr
    fish-register-file-table-row
    [cells]="['register_file.labels.filed_process.filed_process_data.officeAddress.deliverTo' | translate, data().deliverTo]"
  ></tr>
  <tr
    fish-register-file-table-row
    [cells]="['register_file.labels.filed_process.filed_process_data.officeAddress.street' | translate, data().street]"
  ></tr>
  <tr
    fish-register-file-table-row
    [cells]="['register_file.labels.filed_process.filed_process_data.officeAddress.streetNumber' | translate, data().streetNumber]"
  ></tr>
  <tr
    fish-register-file-table-row
    [cells]="['register_file.labels.filed_process.filed_process_data.officeAddress.postcode' | translate, data().postcode]"
  ></tr>
  <tr
    fish-register-file-table-row
    [cells]="['register_file.labels.filed_process.filed_process_data.officeAddress.city' | translate, data().city]"
  ></tr>
  <tr
    fish-register-file-table-row
    [cells]="['register_file.labels.filed_process.filed_process_data.officeAddress.detail' | translate, data().detail]"
  ></tr>
</table>
