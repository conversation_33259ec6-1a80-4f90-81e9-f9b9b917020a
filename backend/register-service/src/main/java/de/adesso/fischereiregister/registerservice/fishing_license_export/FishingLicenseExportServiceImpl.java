package de.adesso.fischereiregister.registerservice.fishing_license_export;


import de.adesso.fischereiregister.core.exceptions.RulesProcessingException;
import de.adesso.fischereiregister.core.model.FishingLicense;
import de.adesso.fischereiregister.core.model.IdentificationDocument;
import de.adesso.fischereiregister.core.model.LimitedLicenseApplication;
import de.adesso.fischereiregister.core.model.LimitedLicenseApproval;
import de.adesso.fischereiregister.core.model.Person;
import de.adesso.fischereiregister.core.model.QualificationsProof;
import de.adesso.fischereiregister.core.model.RegisterEntry;
import de.adesso.fischereiregister.core.model.Tax;
import de.adesso.fischereiregister.core.model.ValidityPeriod;
import de.adesso.fischereiregister.core.model.type.FederalState;
import de.adesso.fischereiregister.core.model.type.LicenseType;
import de.adesso.fischereiregister.core.utils.PersonUtils;
import de.adesso.fischereiregister.registerservice.fishing_license_export.model.RenderedContent;
import de.adesso.fischereiregister.registerservice.fishing_license_export.model.RenderedContentType;
import de.adesso.fischereiregister.registerservice.fishing_license_export.port.TenantRulesValuesPort;
import de.adesso.fischereiregister.registerservice.fishing_license_export.port.model.TaxInformationResult;
import de.adesso.fischereiregister.registerservice.identification_document_view.IdentificationDocumentViewRepository;
import de.adesso.fischereiregister.registerservice.register_entry_view.RegisterEntryView;
import de.adesso.fischereiregister.registerservice.register_entry_view.RegisterEntryViewRepository;
import de.adesso.fischereiregister.registerservice.security.HashingAdapter;
import de.adesso.fischereiregister.registerservice.tenant.TenantConfigurationService;
import de.adesso.fischereiregister.utils.DateUtils;
import de.adesso.fischereiregister.utils.StringNormalizer;
import de.adesso.fischereiregister.view.register_entry_search.RegisterEntrySearchViewRepository;
import jakarta.persistence.EntityNotFoundException;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import net.sf.jasperreports.engine.JRException;
import net.sf.jasperreports.engine.JRRuntimeException;
import net.sf.jasperreports.engine.JasperCompileManager;
import net.sf.jasperreports.engine.JasperExportManager;
import net.sf.jasperreports.engine.JasperFillManager;
import net.sf.jasperreports.engine.JasperPrint;
import net.sf.jasperreports.engine.JasperReport;
import net.sf.jasperreports.engine.data.JRBeanCollectionDataSource;
import org.openapitools.model.SearchItem;
import org.springframework.stereotype.Service;

import java.io.InputStream;
import java.math.BigDecimal;
import java.text.DecimalFormat;
import java.text.DecimalFormatSymbols;
import java.time.LocalDate;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Locale;
import java.util.Map;
import java.util.UUID;


@Slf4j
@Service
@AllArgsConstructor
class FishingLicenseExportServiceImpl implements FishingLicenseExportService {

    private static final String BASE_PATH = "config/export/";

    private static final String REGULAR_LICENSE_TEMPLATE_FILE = "digitalFishingLicense.jrxml";
    private static final String VACATION_FISHING_LICENSE_TEMPLATE_FILE = "digitalVacationFishingLicense.jrxml";
    private static final String LIMITED_FISHING_LICENSE_TEMPLATE_FILE = "digitalLimitedFishingLicense.jrxml";
    private static final String FISHING_TAX_TEMPLATE_FILE = "digitalFishingTax.jrxml";
    private static final String CERTIFICATE_TEMPLATE_FILE = "digitalFishingCertificate.jrxml";
    private static final String LIMITED_LICENSE_APPROVAL_TEMPLATE_FILE = "digitalLimitedLicenseApproval.jrxml";

    private static final String FISHING_LICENSE_FILE_PREFIX = "digitaler-fischereischein";
    private static final String FISHING_TAX_FILE_PREFIX = "digitale-fischereiabgabe";
    private static final String LIMITED_LICENSE_APPROVAL_FILE_PREFIX = "digitale-fischereischein-bewilligung";
    private static final String LIMITED_LICENSE_APPROVAL_PREVIEW_FILE_PREFIX = "digitale-fischereischein-bewilligung-vorschau";
    private static final String FISHING_CERTIFICATE_FILE_PREFIX = "digitale-fischereinachweis";
    private static final String PARAMETER_REGISTER_ENTRY_KEY = "registerEntry";
    private static final String PARAMETER_DOCUMENT_ID_KEY = "documentId";

    private static final String TAX_VALID_FROM = "von";
    private static final String TAX_VALID_TO = "bis";
    private static final String PARAMETER_PERSON = "person";
    private static final String PARAMETER_FISHING_LICENSE_DOCUMENT = "document";
    private static final String PARAMETER_QRCODE = "qrcode";
    private static final String PARAMETER_LICENSE_NUMBER = "licenseNumber";
    private static final String PARAMETER_FORMATED_PASSED_ON = "formatedPassedOn";
    private static final String PARAMETER_FISHING_CERTIFICATE_ID = "fishingCertificateId";
    private static final String PARAMETER_FEE_OD = "feeOD";
    private static final String PARAMETER_FEE_OFFICE = "feeOffice";
    private static final String PARAMETER_TAX = "tax";
    private static final String PARAMETER_FILE_NUMBER = "fileNumber";
    private static final String PARAMETER_EMPLOYEE_PERSONAL_SIGN = "employeePersonalSign";
    private static final String FORMATTED_DATE_TODAY = "formattedDateToday";
    private static final String FORMATTED_LIMITED_LICENSE_APPLICATION_DATE = "formattedLimitedLicenseApplicationDate";
    private static final String SUBJECT = "subject";
    private static final String SUBJECT_SECOND_PART = "subjectSecondPart";
    private static final String SALUTATION = "salutation";
    private static final String FIRST_PARAGRAPH = "firstParagraph";
    private static final String DURATION_PARAGRAPH = "durationParagraph";
    private static final String HEADER_NOTES = "headerNotes";
    private static final String TEXT_NOTES_BULLET_POINTS = "textNotesBulletPoints";
    private static final String HEADER_FEES_AND_TAXES = "headerFeesAndTaxes";
    private static final String TEXT_FEES_AND_TAXES = "textFeesAndTaxes";
    private static final String PAYMENT_ACCOUNT = "paymentAccount";
    private static final String CLOSING = "closing";
    private static final String HEADER_INSTRUCTIONS_ON_LEGAL_REMEDIES = "headerInstructionsOnLegalRemedies";
    private static final String TEXT_INSTRUCTIONS_ON_LEGAL_REMEDIES = "textInstructionsOnLegalRemedies";
    private static final String FOOTER = "footer";
    private static final String OFFICE_ADDRESS = "officeAddress";
    private static final String OFFICE_PHONE = "officePhone";
    private static final String OFFICE_EMAIL = "officeEmail";
    private static final String DELIVERY_ADDRESS = "deliveryAddress";
    private static final String NAME_EMPLOYEE = "nameEmployee";
    private static final String PARAMETER_PREVIEW_TEXT = "previewText";
    private static final String PARAMETER_IS_PREVIEW = "isPreview";

    private static final String LIMITED_LICENSE_APPROVAL_KEY_PREFIX = "export.documents.limited_license_approval.";
    private static final String KEY_SUBJECT = LIMITED_LICENSE_APPROVAL_KEY_PREFIX + "subject";
    private static final String KEY_SUBJECT_SECOND_PART = LIMITED_LICENSE_APPROVAL_KEY_PREFIX + "subject_second_part";
    private static final String KEY_FIRST_PARAGRAPH = LIMITED_LICENSE_APPROVAL_KEY_PREFIX + "first_paragraph";
    private static final String KEY_HEADER_NOTES = LIMITED_LICENSE_APPROVAL_KEY_PREFIX + "header_notes";
    private static final String KEY_TEXT_NOTES_BULLET_POINTS = LIMITED_LICENSE_APPROVAL_KEY_PREFIX + "text_notes_bullet_points";
    private static final String KEY_HEADER_FEES_AND_TAXES = LIMITED_LICENSE_APPROVAL_KEY_PREFIX + "header_fees_and_taxes";
    private static final String KEY_TEXT_FEES_AND_TAXES = LIMITED_LICENSE_APPROVAL_KEY_PREFIX + "text_fees_and_taxes";
    private static final String KEY_PAYMENT_ACCOUNT = LIMITED_LICENSE_APPROVAL_KEY_PREFIX + "payment_account";
    private static final String KEY_CLOSING = LIMITED_LICENSE_APPROVAL_KEY_PREFIX + "closing";
    private static final String KEY_HEADER_INSTRUCTIONS = LIMITED_LICENSE_APPROVAL_KEY_PREFIX + "header_instructions_on_legal_remedies";
    private static final String KEY_TEXT_INSTRUCTIONS = LIMITED_LICENSE_APPROVAL_KEY_PREFIX + "text_instructions_on_legal_remedies";
    private static final String KEY_FOOTER = LIMITED_LICENSE_APPROVAL_KEY_PREFIX + "footer";
    private static final String KEY_OFFICE_ADDRESS = LIMITED_LICENSE_APPROVAL_KEY_PREFIX + "office_address";
    private static final String KEY_SALUTATION = LIMITED_LICENSE_APPROVAL_KEY_PREFIX + "salutation";
    private static final String KEY_DURATION_PARAGRAPH = LIMITED_LICENSE_APPROVAL_KEY_PREFIX + "duration_paragraph";
    private static final String KEY_DURATION_PARAGRAPH_NO_LIMIT = LIMITED_LICENSE_APPROVAL_KEY_PREFIX + "duration_paragraph_no_limit";
    private static final String KEY_PREVIEW_TEXT = LIMITED_LICENSE_APPROVAL_KEY_PREFIX + "preview_text";

    private final RegisterEntryViewRepository registerEntryViewRepository;
    private final RegisterEntrySearchViewRepository registerEntrySearchViewRepository;
    private final IdentificationDocumentViewRepository identificationDocumentViewRepository;
    private final TenantRulesValuesPort tenantRulesValuesService;
    private final HashingAdapter hashingAdapter;
    private final TenantConfigurationService tenantConfigurationService;


    @Override
    public RenderedContent exportFishingLicense(UUID registerId, String documentId) {
        final RegisterEntry registerEntry = getRegisterEntryById(registerId);
        final String salt = getSalt(documentId);

        final IdentificationDocument licenseDocument = getIdentificationDocument(registerEntry, documentId);
        final FishingLicense fishingLicense = licenseDocument.getFishingLicense();
        final String fishingLicenseNumber = fishingLicense.getNumber();

        final String qrCode = hashingAdapter.getQROrNFCDataForLicense(registerId, fishingLicenseNumber, registerEntry.getPerson(), documentId, salt);
        final Person person = registerEntry.getPerson();

        return generatePdfDocumentForFishingLicense(person, licenseDocument, fishingLicenseNumber, documentId, qrCode, fishingLicense);
    }

    @Override
    public RenderedContent exportFishingLicense(UUID registerEntryId, String salt, Person person, IdentificationDocument licenseDocument) {

        final FishingLicense fishingLicense = licenseDocument.getFishingLicense();
        final String fishingLicenseNumber = fishingLicense.getNumber();
        final String documentId = licenseDocument.getDocumentId();
        final String qrCode = hashingAdapter.getQROrNFCDataForLicense(registerEntryId, fishingLicenseNumber, person, documentId, salt);

        return generatePdfDocumentForFishingLicense(person, licenseDocument, fishingLicenseNumber, documentId, qrCode, fishingLicense);
    }

    RenderedContent generatePdfDocumentForFishingLicense(Person person, IdentificationDocument licenseDocument, String fishingLicenseNumber, String documentId, String qrCode, FishingLicense fishingLicense) {
        final Map<String, Object> parameters = new HashMap<>();
        parameters.put(PARAMETER_LICENSE_NUMBER, fishingLicenseNumber);
        parameters.put(PARAMETER_DOCUMENT_ID_KEY, documentId);
        parameters.put(PARAMETER_QRCODE, qrCode);
        parameters.put(PARAMETER_PERSON, person);
        parameters.put(PARAMETER_FISHING_LICENSE_DOCUMENT, licenseDocument);
        final String fishingLicenseTemplatePath = getLicenseTemplatePath(fishingLicense.getIssuingFederalState().toString(), fishingLicense.getType());

        final String filename = getFilename(FISHING_LICENSE_FILE_PREFIX, fishingLicense.getIssuingFederalState().toString(), person);
        final byte[] content = generatePdfContent(fishingLicenseTemplatePath, parameters);

        return new RenderedContent(filename, RenderedContentType.PDF, content);
    }

    @Override
    public RenderedContent exportFishingTaxDocument(UUID registerId, String documentId) {
        final RegisterEntry registerEntry = getRegisterEntryById(registerId);
        final String salt = getSalt(documentId);


        final String qrCode = hashingAdapter.getQROrNFCDataForTax(registerId, registerEntry.getPerson(), documentId, salt);
        final IdentificationDocument taxDocument = getIdentificationDocument(registerEntry, documentId);
        final Tax tax = taxDocument.getTax();
        final Person person = registerEntry.getPerson();

        if (tax != null) {
            return generatePdfDocumentForTax(person, tax, documentId, qrCode);
        } else {
            throw new EntityNotFoundException("document found but did not contain tax information");
        }

    }

    @Override
    public RenderedContent exportFishingTaxDocument(UUID registerEntryId, String salt, Person person, IdentificationDocument document) {
        final String qrCode = hashingAdapter.getQROrNFCDataForTax(registerEntryId, person, document.getDocumentId(), salt);
        final Tax tax = document.getTax();
        final String documentId = document.getDocumentId();

        if (tax != null) {
            return generatePdfDocumentForTax(person, tax, documentId, qrCode);
        } else {
            throw new EntityNotFoundException("document found but contained not tax information");
        }

    }

    RenderedContent generatePdfDocumentForTax(Person person, Tax tax, String documentId, String qrCodeData) {
        final String federalState = tax.getFederalState();

        final Map<String, Object> parameters = new HashMap<>();
        parameters.put(PARAMETER_DOCUMENT_ID_KEY, documentId);
        parameters.put(PARAMETER_QRCODE, qrCodeData);
        parameters.put(PARAMETER_PERSON, person);
        parameters.put(PARAMETER_TAX, tax);
        final String fishingLicenseTemplatePath = getTaxTemplatePath(federalState);

        final String filename = getFilename(FISHING_TAX_FILE_PREFIX, federalState, person);
        // For the taxes a little extra information in the name is required
        final String filenameWithTaxInfo = String.join("-",
                filename,
                TAX_VALID_FROM,
                String.valueOf(tax.getValidFrom().getYear()),
                TAX_VALID_TO,
                String.valueOf(tax.getValidTo().getYear())
        );

        final byte[] content = generatePdfContent(fishingLicenseTemplatePath, parameters);

        return new RenderedContent(filenameWithTaxInfo, RenderedContentType.PDF, content);
    }

    @Override
    public RenderedContent exportFishingCertificate(String fishingCertificateId) throws RulesProcessingException {
        final RegisterEntry registerEntry = getRegisterEntryById(getRegisterEntryIdByFishingCertificateId(fishingCertificateId));

        final QualificationsProof fishingCertificate = registerEntry.getQualificationsProofs().stream().filter(q -> q.getFishingCertificateId().equalsIgnoreCase(fishingCertificateId)).findAny().orElse(null);

        if (fishingCertificate != null) {
            FederalState federalState = FederalState.valueOf(fishingCertificate.getFederalState());

            final String feeOD = tenantRulesValuesService.retrieveFeeDigital(federalState).toString();
            final String feeOffice = tenantRulesValuesService.retrieveFeeAnalog(federalState).toString();
            final Map<String, Object> parameters = new HashMap<>();
            parameters.put(PARAMETER_PERSON, registerEntry.getPerson());
            parameters.put(PARAMETER_FORMATED_PASSED_ON, fishingCertificate.getPassedOn().format(DateUtils.GERMAN_DATE_TIME_FORMATTER));
            parameters.put(PARAMETER_FISHING_CERTIFICATE_ID, fishingCertificateId);
            parameters.put(PARAMETER_FEE_OD, feeOD);
            parameters.put(PARAMETER_FEE_OFFICE, feeOffice);
            final String fishingLicenseTemplatePath = getCertificateTemplatePath(federalState.toString());

            final String filename = getFilename(FISHING_CERTIFICATE_FILE_PREFIX, federalState.toString(), registerEntry.getPerson());
            final byte[] content = generatePdfContent(fishingLicenseTemplatePath, parameters);

            return new RenderedContent(filename, RenderedContentType.PDF, content);

        } else {
            throw new EntityNotFoundException("Document found but is missing fishing certificate information.");
        }
    }

    private String getLicenseTemplatePath(String federalState, LicenseType licenseType) {
        return switch (licenseType) {
            case VACATION -> BASE_PATH + federalState.toLowerCase() + "/" + VACATION_FISHING_LICENSE_TEMPLATE_FILE;
            case LIMITED -> BASE_PATH + federalState.toLowerCase() + "/" + LIMITED_FISHING_LICENSE_TEMPLATE_FILE;
            default -> BASE_PATH + federalState.toLowerCase() + "/" + REGULAR_LICENSE_TEMPLATE_FILE;
        };
    }

    private String getTaxTemplatePath(String federalState) {
        return BASE_PATH + federalState.toLowerCase() + "/" + FISHING_TAX_TEMPLATE_FILE;
    }

    private String getCertificateTemplatePath(String federalState) {
        return BASE_PATH + federalState.toLowerCase() + "/" + CERTIFICATE_TEMPLATE_FILE;
    }

    IdentificationDocument getIdentificationDocument(RegisterEntry registerEntry, String documentId) {
        if (registerEntry.getIdentificationDocuments().isEmpty()) {
            throw new EntityNotFoundException("License document not found in register entry, no documents found");
        }
        return registerEntry.getIdentificationDocuments()
                .stream()
                .filter(d -> d.getDocumentId().equals(documentId))
                .findAny()
                .orElseThrow(EntityNotFoundException::new);
    }

    protected UUID getRegisterEntryIdByFishingCertificateId(String fishingCertificateId) {
        final List<SearchItem> searchItem = registerEntrySearchViewRepository.findByIdentificationNumber(fishingCertificateId).map(List::of).orElse(Collections.emptyList());

        if (!searchItem.isEmpty()) return UUID.fromString(searchItem.getFirst().getRegisterId());
        throw new EntityNotFoundException("Could not find register entry with fishingCertificateId: " + fishingCertificateId);
    }

    private RegisterEntry getRegisterEntryById(UUID registerId) {
        final RegisterEntryView registerEntryView = registerEntryViewRepository.findById(registerId)
                .orElseThrow(EntityNotFoundException::new);
        return registerEntryView.getData();
    }

    private String getSalt(String documentId) {
        return identificationDocumentViewRepository
                .findByIdentificationDocumentId(documentId)
                .orElseThrow(EntityNotFoundException::new).getSalt();
    }

    private byte[] generatePdfContent(String templateFileName, Map<String, Object> parameters) {
        try {
            final InputStream jasperStream = FishingLicenseExportServiceImpl.class.getClassLoader().getResourceAsStream(templateFileName);
            if (jasperStream == null) {
                throw new JRRuntimeException("OSFile not found in classpath: " + templateFileName);
            }
            final JasperReport jasperReport = JasperCompileManager.compileReport(jasperStream);

            final JRBeanCollectionDataSource source = new JRBeanCollectionDataSource(List.of(new Object()));
            final JasperPrint report = JasperFillManager.fillReport(jasperReport, parameters, source);

            return JasperExportManager.exportReportToPdf(report);
        } catch (JRException e) {
            log.error("Error generating pdf report for documentId:{} or registerEntryId:{} using {}",
                    parameters.get(PARAMETER_DOCUMENT_ID_KEY),
                    parameters.containsKey(PARAMETER_REGISTER_ENTRY_KEY) ? ((RegisterEntry) parameters.get(PARAMETER_REGISTER_ENTRY_KEY)).getRegisterId() : null,
                    templateFileName, e);
            throw new JRRuntimeException(e);
        }
    }

    private String getFilename(String filenamePrefix, String federalState, Person person) {

        final String personFullname = PersonUtils.getFullName(person);
        final String formattedPersonFullname = StringNormalizer.normalize(personFullname)
                .replace(".", "")
                .replace(" ", "-");

        return String.join("-", filenamePrefix, formattedPersonFullname, federalState).toLowerCase();
    }

    @Override
    public RenderedContent exportLimitedLicenseApproval(UUID registerEntryId, String documentId) throws RulesProcessingException {
        final RegisterEntry registerEntry = getRegisterEntryById(registerEntryId);

        final IdentificationDocument limitedLicenseApprovalIdentificationDocument = getIdentificationDocument(registerEntry, documentId);
        final LimitedLicenseApproval limitedLicenseApproval = getLimitedLicenseApproval(documentId, limitedLicenseApprovalIdentificationDocument);

        final Person person = registerEntry.getPerson();
        final LimitedLicenseApplication limitedLicenseApplication = registerEntry.getLimitedLicenseApplication();
        final FishingLicense limitedLicense = getLimitedLicense(registerEntry, limitedLicenseApproval);

        if (limitedLicense.getValidityPeriods().size() != 1) {
            throw new IllegalStateException("Tried to generate a limited license approval document for a fishing license with an invalid number of validity periods. amount: " + limitedLicense.getValidityPeriods().size());
        }

        final ValidityPeriod validityPeriod = limitedLicense.getValidityPeriods().getFirst();
        final String issuingFederalState = limitedLicense.getIssuingFederalState().toString();
        final LocalDate createdAt = limitedLicenseApplication != null
                ? limitedLicenseApplication.getCreatedAt()
                : limitedLicenseApproval.getCreatedAt();

        final boolean isPreview = false; // this export is never a preview

        return generatePdfDocumentForLimitedLicenseApproval(
                person,
                validityPeriod,
                issuingFederalState,
                limitedLicenseApproval,
                createdAt,
                isPreview
        );
    }

    @Override
    public RenderedContent exportLimitedLicenseApprovalPreview(Person person, ValidityPeriod validityPeriod, String federalState, LimitedLicenseApproval limitedLicenseApproval) throws RulesProcessingException {
        final boolean isPreview = true;
        final LocalDate createdAt = limitedLicenseApproval.getCreatedAt();
        return generatePdfDocumentForLimitedLicenseApproval(
                person,
                validityPeriod,
                federalState,
                limitedLicenseApproval,
                createdAt,
                isPreview
        );
    }

    private FishingLicense getLimitedLicense(RegisterEntry registerEntry, LimitedLicenseApproval limitedLicenseApproval) {
        return registerEntry.getFishingLicenses().stream()
                .filter(fishingLicense -> fishingLicense.getLimitedLicenseApproval() != null && fishingLicense.getLimitedLicenseApproval().getLimitedLicenseApprovalId().equals(limitedLicenseApproval.getLimitedLicenseApprovalId()))
                .findAny()
                .orElseThrow(() -> new IllegalStateException("Fishing license for limited license approval not found"));
    }

    private LimitedLicenseApproval getLimitedLicenseApproval(String documentId, IdentificationDocument limitedLicenseApprovalIdentificationDocument) {
        final LimitedLicenseApproval limitedLicenseApproval = limitedLicenseApprovalIdentificationDocument.getLimitedLicenseApproval();
        if (limitedLicenseApproval == null) {
            throw new EntityNotFoundException("Limited license approval not found in the identification document for documentId: " + documentId);
        }
        return limitedLicenseApproval;
    }

    private RenderedContent generatePdfDocumentForLimitedLicenseApproval(
            Person person,
            ValidityPeriod validityPeriod,
            String federalState,
            LimitedLicenseApproval limitedLicenseApproval,
            LocalDate createdAt,
            boolean isPreview) throws RulesProcessingException {

        final Map<String, Object> parameters = buildLimitedLicenseApprovalParameters(
                person,
                validityPeriod,
                createdAt,
                limitedLicenseApproval,
                federalState,
                isPreview
        );

        final String templatePath = getLimitedLicenseApprovalTemplatePath(federalState);
        final String filenamePrefix = isPreview ? LIMITED_LICENSE_APPROVAL_PREVIEW_FILE_PREFIX : LIMITED_LICENSE_APPROVAL_FILE_PREFIX;
        final String filename = getFilename(filenamePrefix, federalState, person);
        final byte[] content = generatePdfContent(templatePath, parameters);

        return new RenderedContent(filename, RenderedContentType.PDF, content);
    }

    private BigDecimal calculatePaymentSum(TaxInformationResult taxInformationResult, BigDecimal feeAnalog) {
        return feeAnalog.add(taxInformationResult.netTaxAmount()).add(taxInformationResult.administrativeFee());
    }

    private Map<String, Object> buildLimitedLicenseApprovalParameters(
            Person person,
            ValidityPeriod validityPeriod,
            LocalDate createdAt,
            LimitedLicenseApproval limitedLicenseApproval,
            String federalState,
            Boolean isPreview) throws RulesProcessingException {

        final FederalState state = FederalState.valueOf(federalState);
        final TaxInformationResult taxInformationResult = tenantRulesValuesService.retrieveTaxInformation(state);
        final BigDecimal feeAnalog = tenantRulesValuesService.retrieveFeeAnalog(state);
        final BigDecimal paymentSum = calculatePaymentSum(taxInformationResult, feeAnalog);
        final String createdAtFormattedDate = createdAt.format(DateUtils.GERMAN_DATE_TIME_FORMATTER);
        final String todayFormattedDate = LocalDate.now().format(DateUtils.GERMAN_DATE_TIME_FORMATTER);

        // Approval details
        final String fileNumber = limitedLicenseApproval.getFileNumber();
        final String employeePersonalSign = limitedLicenseApproval.getSigningEmployee().getPersonalSign();
        final String previewText = getTenantValue(federalState, KEY_PREVIEW_TEXT);

        // Subject and salutation
        final String subject = getTenantValue(federalState, KEY_SUBJECT);
        final String subjectSecondPart = buildSubjectSecondPart(createdAtFormattedDate, federalState);
        final String salutation = buildSalutation(person, federalState);

        // Content paragraphs
        final String firstParagraph = getTenantValue(federalState, KEY_FIRST_PARAGRAPH);
        final String durationParagraph = buildDurationParagraph(validityPeriod, limitedLicenseApproval, federalState);

        // Notes section
        final String headerNotes = getTenantValue(federalState, KEY_HEADER_NOTES);
        final String textNotesBulletPoints = getTenantValue(federalState, KEY_TEXT_NOTES_BULLET_POINTS);

        // Fees and taxes
        final String headerFeesAndTaxes = getTenantValue(federalState, KEY_HEADER_FEES_AND_TAXES);
        final String textFeesAndTaxes = buildFeesAndTaxesText(feeAnalog, taxInformationResult, paymentSum, federalState);
        final String paymentAccount = buildPaymentAccount(limitedLicenseApproval, federalState);

        // Closing and legal
        final String closing = getTenantValue(federalState, KEY_CLOSING);
        final String headerInstructions = getTenantValue(federalState, KEY_HEADER_INSTRUCTIONS);
        final String textInstructions = getTenantValue(federalState, KEY_TEXT_INSTRUCTIONS);

        // Contact information
        final String footer = getTenantValue(federalState, KEY_FOOTER);
        final String officeAddress = getTenantValue(federalState, KEY_OFFICE_ADDRESS);
        final String officePhone = limitedLicenseApproval.getSigningEmployee().getPhone();
        final String officeEmail = limitedLicenseApproval.getSigningEmployee().getEmail();
        final String employeeName = limitedLicenseApproval.getSigningEmployee().getName();

        // Delivery information
        final String deliveryAddress = PersonUtils.getPrintableNameAndAddress(person).replace("\n", "<br>");

        Map<String, Object> parameters = new HashMap<>();

        // Approval details
        parameters.put(PARAMETER_FILE_NUMBER, fileNumber);
        parameters.put(PARAMETER_EMPLOYEE_PERSONAL_SIGN, employeePersonalSign);
        parameters.put(FORMATTED_DATE_TODAY, todayFormattedDate);
        parameters.put(FORMATTED_LIMITED_LICENSE_APPLICATION_DATE, createdAtFormattedDate);
        parameters.put(PARAMETER_IS_PREVIEW, isPreview);
        parameters.put(PARAMETER_PREVIEW_TEXT, previewText);

        // Subject and salutation
        parameters.put(SUBJECT, subject);
        parameters.put(SUBJECT_SECOND_PART, subjectSecondPart);
        parameters.put(SALUTATION, salutation);

        // Content paragraphs
        parameters.put(FIRST_PARAGRAPH, firstParagraph);
        parameters.put(DURATION_PARAGRAPH, durationParagraph);

        // Notes section
        parameters.put(HEADER_NOTES, headerNotes);
        parameters.put(TEXT_NOTES_BULLET_POINTS, textNotesBulletPoints);

        // Fees and taxes
        parameters.put(HEADER_FEES_AND_TAXES, headerFeesAndTaxes);
        parameters.put(TEXT_FEES_AND_TAXES, textFeesAndTaxes);
        parameters.put(PAYMENT_ACCOUNT, paymentAccount);

        // Closing and legal
        parameters.put(CLOSING, closing);
        parameters.put(HEADER_INSTRUCTIONS_ON_LEGAL_REMEDIES, headerInstructions);
        parameters.put(TEXT_INSTRUCTIONS_ON_LEGAL_REMEDIES, textInstructions);

        // Contact information
        parameters.put(FOOTER, footer);
        parameters.put(OFFICE_ADDRESS, officeAddress);
        parameters.put(OFFICE_PHONE, officePhone);
        parameters.put(OFFICE_EMAIL, officeEmail);
        parameters.put(NAME_EMPLOYEE, employeeName);

        // Delivery information
        parameters.put(DELIVERY_ADDRESS, deliveryAddress);

        return parameters;
    }

    private String getTenantValue(String federalState, String key) {
        return tenantConfigurationService.getValue(FederalState.valueOf(federalState), key);
    }

    private String buildSubjectSecondPart(String createdAt, String federalState) {
        return getTenantValue(federalState, KEY_SUBJECT_SECOND_PART)
                .replace("{{limitedLicenseApplicationDate}}", createdAt);
    }

    private String buildSalutation(Person person, String federalState) {
        return getTenantValue(federalState, KEY_SALUTATION)
                .replace("{{citizenFullname}}", PersonUtils.getFullName(person));
    }

    private String buildDurationParagraph(ValidityPeriod validityPeriod, LimitedLicenseApproval approval, String federalState) {
        if (validityPeriod.getValidTo() == null) {
            return getTenantValue(federalState, KEY_DURATION_PARAGRAPH_NO_LIMIT);
        }
        return getTenantValue(federalState, KEY_DURATION_PARAGRAPH)
                .replace("{{licenseValidTo}}", validityPeriod.getValidTo().format(DateUtils.GERMAN_DATE_TIME_FORMATTER))
                .replace("{{justificationForLimitedDurationNotice}}", approval.getJustificationForLimitedDurationNotice());
    }

    private String buildFeesAndTaxesText(BigDecimal feeAnalog, TaxInformationResult taxInfo, BigDecimal paymentSum, String federalState) {
        DecimalFormatSymbols symbols = new DecimalFormatSymbols(Locale.GERMANY);
        DecimalFormat decimalFormat = new DecimalFormat("0.00", symbols);

        return getTenantValue(federalState, KEY_TEXT_FEES_AND_TAXES)
                .replace("{{fee}}", decimalFormat.format(feeAnalog))
                .replace("{{netTaxAmount}}", decimalFormat.format(taxInfo.netTaxAmount()))
                .replace("{{administrativeFee}}", decimalFormat.format(taxInfo.administrativeFee()))
                .replace("{{paymentSum}}", decimalFormat.format(paymentSum));
    }

    private String buildPaymentAccount(LimitedLicenseApproval approval, String federalState) {
        return getTenantValue(federalState, KEY_PAYMENT_ACCOUNT)
                .replace("{{cashRegisterSign}}", approval.getCashRegisterSign());
    }

    private String getLimitedLicenseApprovalTemplatePath(String federalState) {
        return BASE_PATH + federalState.toLowerCase() + "/" + LIMITED_LICENSE_APPROVAL_TEMPLATE_FILE;
    }

}
