package de.adesso.fischereiregister.view.filed_process;

import de.adesso.fischereiregister.core.events.BannedEvent;
import de.adesso.fischereiregister.core.events.FishingTaxPayedEvent;
import de.adesso.fischereiregister.core.events.JurisdictionMovedEvent;
import de.adesso.fischereiregister.core.events.LicenseExtendedEvent;
import de.adesso.fischereiregister.core.events.LimitedLicenseApplicationCreatedEvent;
import de.adesso.fischereiregister.core.events.LimitedLicenseApplicationRejectedEvent;
import de.adesso.fischereiregister.core.events.LimitedLicenseCreatedEvent;
import de.adesso.fischereiregister.core.events.PersonCreatedEvent;
import de.adesso.fischereiregister.core.events.QualificationsProofCreatedEvent;
import de.adesso.fischereiregister.core.events.RegisterEntryMarkedForDeletionEvent;
import de.adesso.fischereiregister.core.events.RegularLicenseCreatedEvent;
import de.adesso.fischereiregister.core.events.RegularLicenseDigitizedEvent;
import de.adesso.fischereiregister.core.events.ReplacementCardOrderedEvent;
import de.adesso.fischereiregister.core.events.VacationLicenseCreatedEvent;
import de.adesso.fischereiregister.core.model.Ban;
import de.adesso.fischereiregister.core.model.Jurisdiction;
import de.adesso.fischereiregister.core.model.LimitedLicenseApplication;
import de.adesso.fischereiregister.core.model.Person;
import de.adesso.fischereiregister.core.model.QualificationsProof;
import de.adesso.fischereiregister.core.model.RegisterEntry;
import de.adesso.fischereiregister.core.model.consent.ConsentInfo;
import de.adesso.fischereiregister.core.model.consent.JurisdictionConsentInfo;
import de.adesso.fischereiregister.core.model.consent.TaxConsentInfo;
import de.adesso.fischereiregister.core.model.type.FederalState;
import de.adesso.fischereiregister.core.model.type.LimitedLicenseApplicationStatus;
import de.adesso.fischereiregister.core.model.type.SubmissionType;
import de.adesso.fischereiregister.core.testutils.DomainTestData;
import de.adesso.fischereiregister.view.filed_process.eventhandling.FiledProcessViewHandler;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.time.Instant;
import java.time.LocalDate;
import java.util.UUID;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.ArgumentMatchers.isNull;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;

@ExtendWith(MockitoExtension.class)
class FiledProcessViewHandlerTest {
    @Mock
    FiledProcessViewService service;

    @InjectMocks
    private FiledProcessViewHandler eventHandler;

    @Test
    @DisplayName("FiledProcessViewHandler.on(BannedEvent) should make a call to the filedProcessViewService")
    public void testOnBannedEvent() {
        // ARRANGE
        RegisterEntry entry = DomainTestData.createRegisterEntry();
        final String issuedByOffice = "A in B von C";

        final Ban ban = new Ban();
        ban.setAt(LocalDate.now());
        ban.setBanId(mock(UUID.class));
        ban.setFileNumber("ABCDE");
        ban.setReportedBy(issuedByOffice);
        ban.setFrom(LocalDate.now());
        ban.setTo(LocalDate.now());

        final BannedEvent mockEvent = new BannedEvent(
                entry.getRegisterId(),
                ban,
                entry.getJurisdiction(),
                issuedByOffice
        );

        // ACT
        eventHandler.on(mockEvent, Instant.now());

        // ASSERT
        verify(service, times(1)).createBannedProcess(
                any(),
                eq(ban)
        );
    }

    @Test
    @DisplayName("FiledProcessViewHandler.on(FishingTaxPayedEvent) should make a call to the filedProcessViewService")
    public void testOnFishingTaxPayedEvent() {
        // ARRANGE
        RegisterEntry entry = DomainTestData.createRegisterEntry();
        final TaxConsentInfo consentInfo = DomainTestData.createTaxConsentInfo();
        final String issuedByOffice = "A in B von C";

        final var mockEvent = new FishingTaxPayedEvent(
                entry.getRegisterId(),
                consentInfo,
                entry.getPerson(),
                entry.getTaxes(),
                "someSalt",
                entry.getIdentificationDocuments(),
                issuedByOffice,
                null,
                entry.getServiceAccountId(),
                null,
                SubmissionType.ANALOG
        );

        // ACT
        eventHandler.on(mockEvent, Instant.now());

        // ASSERT
        verify(service, times(1)).createFishingTaxPayedProcess(
                any(),
                eq(entry.getPerson()),
                eq(entry.getServiceAccountId()),
                eq(entry.getTaxes()),
                eq(consentInfo),
                eq(entry.getIdentificationDocuments())
        );
    }

    @Test
    @DisplayName("FiledProcessViewHandler.on(JurisdictionMovedEvent) should make a call to the filedProcessViewService")
    public void testOnJurisdictionMovedEvent() {
        // ARRANGE
        RegisterEntry entry = DomainTestData.createRegisterEntry();
        final Jurisdiction previousJurisdiction = DomainTestData.createJurisdiction();
        final Jurisdiction newJurisdiction = DomainTestData.createJurisdiction();
        final JurisdictionConsentInfo consentInfo = DomainTestData.createJurisdictionConsentInfo();
        final String issuedByOffice = "A in B von C";

        final var mockEvent = new JurisdictionMovedEvent(
                entry.getRegisterId(),
                previousJurisdiction,
                newJurisdiction,
                consentInfo,
                null,
                entry.getTaxes(),
                "someSalt",
                issuedByOffice,
                entry.getIdentificationDocuments(),
                SubmissionType.ANALOG
        );

        // ACT
        eventHandler.on(mockEvent, Instant.now());

        // ASSERT
        verify(service, times(1)).createJurisdictionMovedProcess(
                any(),
                eq(entry.getTaxes()),
                eq(consentInfo),
                eq(entry.getIdentificationDocuments()),
                eq(newJurisdiction)
        );
    }

    @Test
    @DisplayName("FiledProcessViewHandler.on(LicenseExtendedEvent) should make a call to the filedProcessViewService")
    public void testOnLicenseExtendedEvent() {
        // ARRANGE
        RegisterEntry entry = DomainTestData.createRegisterEntry();
        final String licenseNumber = "12345-EXT";
        final String issuedByOffice = "A in B von C";

        final var mockEvent = new LicenseExtendedEvent(
                entry.getRegisterId(),
                entry.getPerson(),
                "someSalt",
                DomainTestData.createConsentInfo(),
                entry.getFees(),
                entry.getTaxes(),
                licenseNumber,
                DomainTestData.createValidityPeriod(),
                entry.getIdentificationDocuments(),
                null,
                entry.getServiceAccountId(),
                null,
                issuedByOffice,
                SubmissionType.ANALOG
        );

        // ACT
        eventHandler.on(mockEvent, Instant.now());

        // ASSERT
        verify(service, times(1)).createFishingLicenseExtendedProcess(
                any(),
                eq(entry.getPerson()),
                eq(entry.getServiceAccountId()),
                eq(entry.getTaxes()),
                eq(entry.getFees()),
                eq(entry.getIdentificationDocuments()),
                eq(licenseNumber),
                eq(entry.getPerson().getOfficeAddress())
        );
    }

    @Test
    @DisplayName("FiledProcessViewHandler.on(LimitedLicenseCreatedEvent) should make a call to the filedProcessViewService")
    public void testOnLimitedLicenseCreatedEvent() {
        // ARRANGE
        RegisterEntry entry = DomainTestData.createRegisterEntry();
        final String issuedByOffice = "A in B von C";
        final String issuedByAddress = "A Straße, 12345 C";

        final var mockEvent = new LimitedLicenseCreatedEvent(
                entry.getRegisterId(),
                "someSalt",
                DomainTestData.createLimitedLicenseConsentInfo(),
                entry.getPerson(),
                entry.getFees(),
                entry.getTaxes(),
                entry.getFishingLicenses().getFirst(),
                entry.getIdentificationDocuments(),
                entry.getJurisdiction(),
                issuedByOffice,
                issuedByAddress,
                null,
                entry.getServiceAccountId(),
                null,
                SubmissionType.ANALOG
        );

        // ACT
        eventHandler.on(mockEvent, Instant.now());

        // ASSERT
        verify(service, times(1)).createFishingLicenseCreatedProcess(
                any(),
                eq(entry.getPerson()),
                eq(entry.getServiceAccountId()),
                eq(entry.getTaxes()),
                eq(entry.getFees()),
                eq(entry.getIdentificationDocuments()),
                eq(entry.getFishingLicenses().getFirst()),
                eq(entry.getPerson().getOfficeAddress())
        );
    }

    @Test
    @DisplayName("FiledProcessViewHandler.on(LimitedLicenseApplicationCreatedEvent) should make a call to the filedProcessViewService")
    public void testOnLimitedLicenseApplicationCreatedEvent() {
        // ARRANGE
        RegisterEntry entry = DomainTestData.createRegisterEntry();
        final ConsentInfo consentInfo = DomainTestData.createConsentInfo();

        final LimitedLicenseApplication limitedLicenseApplication = new LimitedLicenseApplication();
        limitedLicenseApplication.setStatus(LimitedLicenseApplicationStatus.PENDING);
        limitedLicenseApplication.setCreatedAt(LocalDate.now());
        limitedLicenseApplication.setFederalState(FederalState.SH);

        final var mockEvent = new LimitedLicenseApplicationCreatedEvent(
                entry.getRegisterId(),
                limitedLicenseApplication,
                entry.getPerson(),
                entry.getFees(),
                consentInfo,
                null,
                entry.getServiceAccountId()
        );

        // ACT
        eventHandler.on(mockEvent, Instant.now());

        // ASSERT
        verify(service, times(1)).createLimitedLicenseApplicationProcess(
                any(),
                eq(entry.getPerson()),
                eq(entry.getServiceAccountId()),
                eq(entry.getFees()),
                eq(consentInfo)
        );
    }

    @Test
    @DisplayName("FiledProcessViewHandler.on(LimitedLicenseApplicationRejectedEvent) should make a call to the filedProcessViewService")
    public void testOnLimitedLicenseApplicationRejectedEvent() {
        // ARRANGE
        RegisterEntry entry = DomainTestData.createRegisterEntry();

        final LimitedLicenseApplication limitedLicenseApplication = new LimitedLicenseApplication();
        limitedLicenseApplication.setStatus(LimitedLicenseApplicationStatus.REJECTED);
        limitedLicenseApplication.setCreatedAt(LocalDate.now());
        limitedLicenseApplication.setFederalState(FederalState.SH);

        final var mockEvent = new LimitedLicenseApplicationRejectedEvent(
                entry.getRegisterId(),
                limitedLicenseApplication,
                entry.getPerson(),
                null,
                null
        );

        // ACT
        eventHandler.on(mockEvent, Instant.now());

        // ASSERT
        verify(service, times(1)).createLimitedLicenseRejectedProcess(any());
    }

    @Test
    @DisplayName("FiledProcessViewHandler.on(PersonCreatedEvent) should make a call to the filedProcessViewService")
    public void testOnPersonCreatedEvent() {
        // ARRANGE
        RegisterEntry entry = DomainTestData.createRegisterEntry();
        final String issuedByOffice = "A in B von C";
        final TaxConsentInfo consentInfo = DomainTestData.createTaxConsentInfo();

        final var mockEvent = new PersonCreatedEvent(
                entry.getRegisterId(),
                entry.getPerson(),
                entry.getTaxes(),
                entry.getTaxes(),
                "someSalt",
                entry.getIdentificationDocuments(),
                consentInfo,
                issuedByOffice,
                null,
                entry.getServiceAccountId(),
                null,
                "SH",
                SubmissionType.ANALOG
        );

        // ACT
        eventHandler.on(mockEvent, Instant.now());

        // ASSERT
        verify(service, times(1)).createFishingTaxPayedProcess(
                any(),
                eq(entry.getPerson()),
                eq(entry.getServiceAccountId()),
                eq(entry.getTaxes()),
                eq(consentInfo),
                eq(entry.getIdentificationDocuments())
        );
    }

    @Test
    @DisplayName("FiledProcessViewHandler.on(QualificationsProofCreatedEvent) should make a call to the filedProcessViewService")
    public void testOnQualificationsProofCreatedEvent() {
        // ARRANGE
        final UUID registerEntryId = DomainTestData.registerId;
        final QualificationsProof proof = DomainTestData.createQualificationsProof();
        final Person person = DomainTestData.createPerson();

        final var mockEvent = new QualificationsProofCreatedEvent(registerEntryId, proof, person);

        // ACT
        eventHandler.on(mockEvent, Instant.now());

        // ASSERT
        verify(service, times(1)).createQualificationsProofCreatedProcess(
                any(),
                eq(person),
                eq(proof)
        );
    }

    @Test
    @DisplayName("FiledProcessViewHandler.on(RegularLicenseDigitizedEvent) should make a call to the filedProcessViewService")
    public void testOnRegularLicenseCreatedEvent() {
        // ARRANGE
        RegisterEntry entry = DomainTestData.createRegisterEntry();
        final ConsentInfo consentInfo = DomainTestData.createConsentInfo();
        final String issuedByOffice = "A in B von C";
        final String issuedByAddress = "A Straße, 12345 C";

        final var mockEvent = new RegularLicenseCreatedEvent(
                entry.getRegisterId(),
                null,
                consentInfo,
                entry.getPerson(),
                entry.getFees(),
                entry.getTaxes(),
                entry.getFishingLicenses().getFirst(),
                entry.getIdentificationDocuments(),
                entry.getJurisdiction(),
                issuedByOffice,
                issuedByAddress,
                null,
                entry.getServiceAccountId(),
                null,
                SubmissionType.ONLINE
        );

        // ACT
        eventHandler.on(mockEvent, Instant.now());

        // ASSERT
        verify(service, times(1)).createFishingLicenseCreatedProcess(
                any(),
                eq(entry.getPerson()),
                eq(entry.getServiceAccountId()),
                eq(entry.getTaxes()),
                eq(entry.getFees()),
                eq(entry.getIdentificationDocuments()),
                eq(entry.getFishingLicenses().getFirst()),
                eq(entry.getPerson().getOfficeAddress())
        );
    }

    @Test
    @DisplayName("FiledProcessViewHandler.on(RegularLicenseDigitizedEvent) should make a call to the filedProcessViewService")
    public void testOnRegularLicenseDigitizedEvent() {
        // ARRANGE
        RegisterEntry entry = DomainTestData.createRegisterEntry();

        final ConsentInfo consentInfo = DomainTestData.createConsentInfo();
        final String issuedByOffice = "A in B von C";
        final String issuedByAddress = "A Straße, 12345 C";

        final var mockEvent = new RegularLicenseDigitizedEvent(
                entry.getRegisterId(),
                null,
                entry.getPerson(),
                entry.getJurisdiction(),
                entry.getFishingLicenses().getFirst(),
                entry.getFees(),
                entry.getTaxes(),
                entry.getQualificationsProofs(),
                entry.getIdentificationDocuments(),
                consentInfo,
                issuedByOffice,
                issuedByAddress
        );

        // ACT
        eventHandler.on(mockEvent, Instant.now());

        // ASSERT
        verify(service, times(1)).createFishingLicenseCreatedProcess(
                any(),
                eq(entry.getPerson()),
                isNull(),
                eq(entry.getTaxes()),
                eq(entry.getFees()),
                eq(entry.getIdentificationDocuments()),
                eq(entry.getFishingLicenses().getFirst()),
                eq(entry.getPerson().getOfficeAddress())
        );
    }

    @Test
    @DisplayName("FiledProcessViewHandler.on(ReplacementCardOrderedEvent) should make a call to the filedProcessViewService")
    public void testOnReplacementCardOrderedEvent() {
        // ARRANGE
        RegisterEntry entry = DomainTestData.createRegisterEntry();
        final ConsentInfo consentInfo = DomainTestData.createConsentInfo();
        final String issuedByOffice = "A in B von C";
        final String issuedByAddress = "A Straße, 12345 C";

        final var mockEvent = new ReplacementCardOrderedEvent(
                entry.getRegisterId(),
                entry.getFishingLicenses().getFirst(),
                entry.getPerson(),
                entry.getIdentificationDocuments(),
                null,
                FederalState.SH,
                issuedByOffice,
                issuedByAddress,
                entry.getFees(),
                entry.getTaxes(),
                consentInfo,
                null,
                entry.getServiceAccountId(),
                null,
                SubmissionType.ONLINE
        );

        // ACT
        eventHandler.on(mockEvent, Instant.now());

        // ASSERT
        verify(service, times(1)).createReplacementCardOrderedProcess(
                any(),
                eq(entry.getPerson()),
                eq(entry.getServiceAccountId()),
                eq(entry.getTaxes()),
                eq(entry.getFees()),
                eq(entry.getIdentificationDocuments()),
                eq(entry.getFishingLicenses().getFirst()),
                eq(entry.getPerson().getOfficeAddress())
        );
    }

    @Test
    @DisplayName("FiledProcessViewHandler.on(VacationLicenseCreatedEvent) should make a call to the filedProcessViewService")
    public void testOnVacationLicenseCreatedEvent() {
        // ARRANGE
        RegisterEntry entry = DomainTestData.createRegisterEntry();
        final String issuedByOffice = "A in B von C";

        final var mockEvent = new VacationLicenseCreatedEvent(
                entry.getRegisterId(),
                entry.getPerson(),
                "someSalt",
                DomainTestData.createConsentInfo(),
                entry.getFees(),
                entry.getTaxes(),
                entry.getIdentificationDocuments(),
                entry.getFishingLicenses().getFirst(),
                issuedByOffice,
                null,
                entry.getServiceAccountId(),
                null,
                SubmissionType.ANALOG
        );

        // ACT
        eventHandler.on(mockEvent, Instant.now());

        // ASSERT
        verify(service, times(1)).createFishingLicenseCreatedProcess(
                any(),
                eq(entry.getPerson()),
                eq(entry.getServiceAccountId()),
                eq(entry.getTaxes()),
                eq(entry.getFees()),
                eq(entry.getIdentificationDocuments()),
                eq(entry.getFishingLicenses().getFirst()),
                eq(entry.getPerson().getOfficeAddress())
        );
    }

    @Test
    @DisplayName("FiledProcessViewHandler.on(RegisterEntryMarkedForDeletionEvent) should make a call to the filedProcessViewService")
    public void testOnRegisterDeletedEvent() {
        // ARRANGE
        RegisterEntry entry = DomainTestData.createRegisterEntry();
        final String issuedByOffice = "Kiel";

        final var mockEvent = new RegisterEntryMarkedForDeletionEvent(
                entry.getRegisterId(),
                entry.getDeletionFlag(),
                issuedByOffice,
                FederalState.SH.toString()
        );

        // ACT
        eventHandler.on(mockEvent, Instant.now());

        // ASSERT
        verify(service, times(1)).createMarkedForDeletionProcess(
                any(),
                eq(entry.getDeletionFlag())
        );
    }
}
